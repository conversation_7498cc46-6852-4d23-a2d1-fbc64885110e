<!DOCTYPE html>
<html lang="<%= I18n.locale %>">
  <head>
    <% if Rails.env.development? %>
      <title>Týmbox - SPA</title>
    <% else %>
      <title>Online docházkový systém zdarma | Týmbox | Docházka do mobilu</title>
    <% end %>
    <meta name="description" content="Online docházka zdarma pro malé týmy. Jednoduchý docházkový systém do mobilu bez instalace. Zaznamenávejte pracovní dobu odkudkoliv s Týmboxem.">

    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= request.original_url %>">
    <meta property="og:title" content="Docházkový systém zdarma | Online evidence pracovní doby">
    <meta property="og:description" content="Jednoduchý online docházkový systém zdarma. Mobilní docházka bez instalace, měsíční výkazy práce online.">

    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<%= request.original_url %>">
    <meta property="twitter:title" content="Docházkový systém zdarma | Online evidence pracovní doby">
    <meta property="twitter:description" content="Jednoduchý online docházkový systém zdarma. Mobilní docházka bez instalace.">

    <link rel="canonical" href="<%= request.original_url %>">
    
    <meta name="keywords" content="docházkový systém zdarma, online docházka, evidence pracovní doby, mobilní docházka, měsíční výkazy práce, Týmbox">

    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%# JWT-only mode - CSRF tokens not needed %>
    <%#= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= vite_client_tag %>
    <%= vite_javascript_tag 'application' %>
    
    <link rel="icon" type="image/png" href="/favicon.ico">
    
    <% if FeatureFlags.pwa_enabled? %>
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- PWA Meta Tags -->
    <meta name="application-name" content="Týmbox">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Týmbox">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="msapplication-TileColor" content="#3b82f6">
    <meta name="msapplication-tap-highlight" content="no">
    
    <!-- PWA Icons -->
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/icons/icon-512x512.png">
    <% end %> 
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-HWE817PRNM"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-HWE817PRNM');
    </script>
  </head>

  <body>
    <!-- Flash messages data for Vue app -->
    <div id="flash-messages" 
      data-messages="<%= {
        notice: flash[:notice],
        alert: flash[:alert],
        error: flash[:error],
        analytics_event: flash[:analytics_event]
      }.to_json %>"
      style="display: none;">
    </div>
    
    <!-- SPA mount point -->
    <div id="app"></div>
  </body>
</html>