import '../css/main.css';
import '../css/application.css';
import '../utils/axiosSetup'; 
import axios from 'axios';

// DEBUG: Check axios instance after import
console.log('[DEBUG] application.js: axios instance ID:', axios._axiosId);
console.log('[DEBUG] application.js: axios interceptors:', {
  request: axios.interceptors?.request?.handlers?.length,
  response: axios.interceptors?.response?.handlers?.length
});

// Development-only console forwarding - tree-shaken in production
if (import.meta.env.DEV) {
  import('../dev/console-forwarder');
}
import { createApp } from 'vue';  
import store from '../store';
import i18n, { loadLocaleMessages, getStartingLocale } from '../i18n';
import router from '../router';
import App from '../App.vue';
import AuthService from '../services/authService';

// Import components to be registered globally
import EventForm from '../components/events/EventForm.vue';
import WorkForm from '../components/works/WorkForm.vue';
import ServiceContractForm from '../components/works/ServiceContractForm.vue';
import WorkShow from '../components/works/WorkShow.vue';
import MeetingForm from '../components/meetings/MeetingForm.vue';
import CookieConsent from '../components/CookieConsent.vue';

// JWT-only authentication mode - no CSRF tokens needed
// All authentication is handled via JWT tokens in Authorization headers

// Paths that do not require an authenticated user
// The app will skip the initial authentication check on these routes
const AUTH_BYPASS_PATHS = [
  '/login',
  '/register',           // Primary registration path
  '/users/sign_up',      // Legacy Devise registration path  
  '/users/password',     // Devise "forgot password" form path
  'confirm-email',       // Email confirmation (TYM-37 fix) - no leading slash for locale paths
  'forgot-password',     // Primary "forgot password" path - no leading slash for locale paths
  'reset-password',      // Password reset form - no leading slash for locale paths
  'auth/accept-invitation'  // Invitation acceptance - no leading slash for locale paths
];

// Set default headers for JSON requests
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';
axios.defaults.headers.patch['Content-Type'] = 'application/json';

// Initialize app data
async function initializeApp() {
  try {
    // Load locale messages
    const locale = getStartingLocale();
    await loadLocaleMessages(i18n, locale);
    
    // Check if we're on an auth page - skip authenticated data fetching
    // JWT-only mode: Updated to use consistent auth paths
    console.log('[DEBUG] Checking auth bypass for path:', window.location.pathname);
    console.log('[DEBUG] AUTH_BYPASS_PATHS:', AUTH_BYPASS_PATHS);
    const isAuthPage = AUTH_BYPASS_PATHS.some(path => {
      const matches = window.location.pathname.includes(path);
      console.log('[DEBUG] Path check:', path, 'matches:', matches);
      return matches;
    });
    
    if (isAuthPage) {
      console.log('Auth page detected - skipping authenticated data fetching');
      return { current_plan: 'free', available_features: [] };
    }
    
    // Ensure user is authenticated before proceeding
    const isAuthenticated = await AuthService.ensureAuthenticated();
    if (!isAuthenticated) {
      // If not authenticated, let the interceptor handle redirect.
      // Return default values to prevent app from crashing.
      return { current_plan: 'free', available_features: [] };
    }
    
    // Fetch initial data for authenticated pages
    const subscriptionResponse = await axios.get('/api/v1/subscription_status');
    window.appSubscription = subscriptionResponse.data;
    
    // User data is already fetched by ensureAuthenticated
    
    return subscriptionResponse.data;
  } catch (error) {
    console.error('Error initializing app:', error);
    // If not authenticated, return default values
    return { current_plan: 'free', available_features: [] };
  }
}

// Register service worker for PWA functionality
async function registerServiceWorker() {
  if ('serviceWorker' in navigator) {
    try {
      console.log('[PWA] Registering service worker...');
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });
      
      console.log('[PWA] Service worker registered successfully:', registration);
      
      // Handle updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('[PWA] New service worker installed, prompting for update');
              // In a real app, show update prompt to user
              newWorker.postMessage({ type: 'SKIP_WAITING' });
            }
          });
        }
      });
      
      return registration;
      
    } catch (error) {
      console.error('[PWA] Service worker registration failed:', error);
      return null;
    }
  } else {
    console.log('[PWA] Service workers not supported');
    return null;
  }
}

// Create and mount the app
document.addEventListener('DOMContentLoaded', async () => {
  // Register service worker if PWA is enabled
  // Allow localhost, local IPs, and HTTPS
  const isLocalDev = window.location.hostname === 'localhost' || 
                     window.location.hostname === '************' ||
                     window.location.hostname.startsWith('192.168.');
  const isSecure = window.location.protocol === 'https:';
  
  if (isLocalDev || isSecure) {
    await registerServiceWorker();
  } else {
    console.log('[PWA] Service worker registration skipped (requires HTTPS or local development)');
  }
  
  // Check if we're on a SPA page
  const appElement = document.getElementById('app');
  
  if (appElement) {
    // SPA mode - mount the full app
    const subscriptionData = await initializeApp();
    
    // Create the Vue app
    const app = createApp(App);
    
    // Register global components
    app.component('EventForm', EventForm);
    app.component('WorkForm', WorkForm);
    app.component('ServiceContractForm', ServiceContractForm);
    app.component('MeetingForm', MeetingForm);
    app.component('CookieConsent', CookieConsent);
    
    // Use plugins
    app.use(store);
    app.use(i18n);
    app.use(router);
    
    // Provide subscription data globally
    app.provide('subscription', subscriptionData);
    
    // Expose router, store, and i18n globally for axios interceptor and debugging
    window.$router = router;
    window.$store = store;
    window.$i18n = i18n;
    
    // Mount the app
    app.mount('#app');
  } else {
    // Legacy mode - mount individual components
    await mountLegacyComponents();
  }
  
  // Handle analytics if needed
  const flashMessages = JSON.parse(document.getElementById('flash-messages')?.dataset.messages || '{}');
  if (flashMessages.analytics_event === "registration_complete") {
    if (typeof gtag !== 'undefined') {
      gtag('event', 'conversion', {
        'event_category': 'registration',
        'event_label': 'user_signup',
        'send_to': 'G-HWE817PRNM'
      });
      gtag('event', 'user_registration');
    }
  }
});

// Legacy component mounting for non-SPA pages
async function mountLegacyComponents() {
  // Import components for legacy mode
  const { createApp: createLegacyApp } = await import('vue');
  const FlashMessages = (await import('../components/FlashMessages.vue')).default;
  const Sidebar = (await import('../components/Sidebar.vue')).default;
  const Topbar = (await import('../components/Topbar.vue')).default;
  const Mainbox = (await import('../components/Mainbox.vue')).default;
  const DailyLogsApp = (await import('../components/dailylogs/DailyLogsIndex.vue')).default;
  const EventList = (await import('../components/events/EventList.vue')).default;
  const CompanyIndex = (await import('../components/companies/CompanyIndex.vue')).default;
  const MonthlyReport = (await import('../components/MonthlyReport.vue')).default;
  const CompanyConnections = (await import('../components/CompanyConnections.vue')).default;
  const ContractsList = (await import('../components/contracts/ContractsList.vue')).default;
  const WorksIndex = (await import('../components/works/WorksIndex.vue')).default;
  const BookingsIndex = (await import('../components/bookings/BookingsIndex.vue')).default;
  const UserSettingsForm = (await import('../components/user_profile/UserSettingsForm.vue')).default;
  const ActivityDashboard = (await import('../components/reports/ActivityDashboard.vue')).default;
  const CentralModal = (await import('../components/shared/CentralModal.vue')).default;
  const Holidays = (await import('../components/Holidays.vue')).default;

  // Initialize app data
  try {
    // Check if we're on an auth page - skip authenticated data fetching
    console.log('[DEBUG] Legacy components: Checking auth bypass for path:', window.location.pathname);
    const isAuthPage = AUTH_BYPASS_PATHS.some(path => {
      const matches = window.location.pathname.includes(path);
      console.log('[DEBUG] Legacy path check:', path, 'matches:', matches);
      return matches;
    });
    
    if (!isAuthPage) {
      // Ensure user is authenticated before proceeding
      const isAuthenticated = await AuthService.ensureAuthenticated();
      if (isAuthenticated) {
        const subscriptionResponse = await axios.get('/api/v1/subscription_status');
        window.appSubscription = subscriptionResponse.data;
        // User data is already fetched by ensureAuthenticated
      }
    }
  } catch (error) {
    console.error('Error initializing app data:', error);
  }

  // Load translations
  const locale = getStartingLocale();
  await loadLocaleMessages(i18n, locale);

  // Create a mini router for legacy mode navigation
  const legacyRouter = {
    push: (path) => {
      window.location.href = path;
    }
  };

  // Mount individual components
  const mountComponent = (selector, Component, props = {}) => {
    const element = document.querySelector(selector);
    if (element) {
      const app = createLegacyApp(Component, props);
      app.use(store);
      app.use(i18n);
      // Make router available globally for components
      app.config.globalProperties.$router = legacyRouter;
      app.mount(element);
    }
  };

  // Mount flash messages
  mountComponent('#flash-messages', FlashMessages);

  // Mount cookie consent
  mountComponent('#cookie-consent', CookieConsent);

  // Mount sidebar
  const sidebarElement = document.getElementById('sidebar-app');
  if (sidebarElement) {
    const propsData = { ...sidebarElement.dataset };
    if (propsData.isWorking) {
      propsData.isWorking = propsData.isWorking === 'true';
    }
    mountComponent('#sidebar-app', Sidebar, propsData);
  }

  // Mount topbar
  const topbarElement = document.getElementById('topbar-app');
  if (topbarElement) {
    mountComponent('#topbar-app', Topbar, { ...topbarElement.dataset });
  }

  // Mount other components
  mountComponent('#holidays-app', Holidays);
  mountComponent('#daily-logs-index', DailyLogsApp);
  mountComponent('#event-list', EventList);
  mountComponent('#company-list', CompanyIndex);
  mountComponent('#works-index', WorksIndex);
  mountComponent('#bookings-index', BookingsIndex);
  mountComponent('#user-settings-form', UserSettingsForm);
  mountComponent('#mainbox', Mainbox, { subscription: window.appSubscription });
  mountComponent('#monthly-report', MonthlyReport);
  mountComponent('#contracts-list', ContractsList);
  mountComponent('#activity-dashboard', ActivityDashboard);

  // Mount company connections
  const companyConnectionsElement = document.getElementById('company-connections');
  if (companyConnectionsElement) {
    mountComponent('#company-connections', CompanyConnections, { 
      embedded: companyConnectionsElement.dataset.embedded 
    });
  }

  // Mount central modal with global components
  const centralModalElement = document.getElementById('central-modal-app');
  if (centralModalElement) {
    const modalApp = createLegacyApp(CentralModal);
    modalApp.component('EventForm', EventForm);
    modalApp.component('WorkForm', WorkForm);
    modalApp.component('ServiceContractForm', ServiceContractForm);
    modalApp.component('WorkShow', WorkShow);
    modalApp.component('MeetingForm', MeetingForm);
    modalApp.use(store);
    modalApp.use(i18n);
    modalApp.mount(centralModalElement);
  }
}