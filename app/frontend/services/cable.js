import { createConsumer } from '@rails/actioncable'
import store from '../store'

// TODO (Future Enhancement): Implement CableService as EventEmitter
// - Emit cable:connected, cable:disconnected, cable:rejected events
// - Allow components to listen for connection state changes
// - Provide better UI feedback for connection status

// TODO (Future Enhancement): Implement stale connection detection
// - Use Page Visibility API to detect when tab becomes visible
// - Check connection status and refresh token if needed
// - Automatically reconnect with fresh JWT when user returns

class CableService {
  constructor() {
    this.consumer = null
    this.subscriptions = new Map()
    this.connectionPromise = null
    this.subscriptionSnapshots = new Map() // Store subscription details for reconnection
  }

  connect() {
    // Don't connect if already connected
    if (this.consumer && this.consumer.connection.isOpen()) {
      console.log('[Cable] Already connected')
      return Promise.resolve(this.consumer)
    }

    // Return existing connection promise if already connecting
    if (this.connectionPromise) {
      console.log('[Cable] Connection already in progress')
      return this.connectionPromise
    }

    // Create new connection promise with guaranteed cleanup
    this.connectionPromise = new Promise((resolve, reject) => {
      // Get the JWT token from Vuex store
      const token = store.getters['userStore/jwtToken']
      
      if (!token) {
        console.warn('[Cable] No JWT token available, cannot connect to Action Cable')
        reject(new Error('No JWT token available'))
        return
      }

      let connectionTimeout = null

      try {
        // Create consumer with JWT token in query params
        // This is the most reliable way to pass the token to WebSocket connections
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        const host = window.location.host
        const cableUrl = `${protocol}//${host}/cable?token=${encodeURIComponent(token)}`

        console.log('🟡 [Cable] Attempting connection to:', cableUrl)
        console.log('🔑 [Cable] Token length:', token.length, 'characters')
        console.log('🌐 [Cable] Browser protocol:', window.location.protocol)
        console.log('🌐 [Cable] Browser host:', window.location.host)
        console.log('🌐 [Cable] Full browser URL:', window.location.href)
        
        console.log('🔧 [Cable] About to call createConsumer...')
        this.consumer = createConsumer(cableUrl)
        console.log('🔧 [Cable] createConsumer returned:', this.consumer)
        console.log('🔧 [Cable] Consumer connection state:', this.consumer?.connection?.state)
        console.log('🔧 [Cable] Consumer connection URL:', this.consumer?.connection?.url)
        console.log('🔧 [Cable] Consumer connection object:', this.consumer?.connection)
        console.log('🔧 [Cable] Consumer connection events:', this.consumer?.connection?.events)

        // Add connection event listeners using correct Action Cable API
        console.log('🔧 [Cable] Setting up connection event listeners...')
        
        // Use the correct event listeners for Action Cable
        this.consumer.connection.events.open = () => {
          console.log('✅ [Cable] Successfully connected to Action Cable')
          clearTimeout(connectionTimeout)
          // Restore subscriptions after reconnection
          this.restoreSubscriptions()
          resolve(this.consumer)
        }

        this.consumer.connection.events.close = () => {
          console.log('🔌 [Cable] Disconnected from Action Cable')
        }

        this.consumer.connection.events.error = (error) => {
          console.error('❌ [Cable] Connection error:', error)
          clearTimeout(connectionTimeout)
          this.consumer = null
          reject(new Error('Connection error: ' + error))
        }
        
        // Explicitly open the connection
        console.log('🔧 [Cable] Explicitly opening connection...')
        this.consumer.connection.open()

        // Add a connection timeout to prevent indefinite waiting
        connectionTimeout = setTimeout(() => {
          console.error('🔴 [Cable] Connection timeout - failed to establish connection within 30 seconds')
          console.error('🔴 [Cable] URL attempted:', cableUrl)
          this.consumer = null
          reject(new Error('Connection timeout'))
        }, 30000) // 30 second timeout

      } catch (error) {
        // Handle any synchronous errors from createConsumer
        console.error('❌ [Cable] Failed to create consumer:', error)
        clearTimeout(connectionTimeout)
        this.consumer = null
        reject(error)
      }
    }).finally(() => {
      // CRITICAL: Always clear connectionPromise regardless of outcome
      // This prevents deadlock in ALL cases - success, failure, timeout, or any edge case
      this.connectionPromise = null
      console.log('[Cable] Connection promise cleared')
    })

    return this.connectionPromise
  }

  disconnect() {
    if (this.consumer) {
      console.log('[Cable] Disconnecting from Action Cable')
      
      // Take snapshot of current subscriptions before disconnecting
      this.takeSubscriptionSnapshot()
      
      // Unsubscribe from all channels
      this.subscriptions.forEach((subscription, key) => {
        this.consumer.subscriptions.remove(subscription)
      })
      this.subscriptions.clear()

      // Disconnect the consumer
      this.consumer.disconnect()
      this.consumer = null
    }
  }

  reconnect() {
    console.log('[Cable] Reconnecting to Action Cable')
    this.disconnect()
    return this.connect()
  }

  // Subscribe to a channel
  async subscribe(channelName, channelParams = {}, callbacks = {}) {
    // Ensure connection exists or create one
    if (!this.consumer || !this.consumer.connection.isOpen()) {
      try {
        await this.connect()
      } catch (error) {
        console.error('[Cable] Failed to connect:', error)
        // Still store the subscription snapshot for future reconnection attempts
        const key = `${channelName}-${JSON.stringify(channelParams)}`
        this.subscriptionSnapshots.set(key, {
          channelName,
          channelParams,
          callbacks
        })
        // Notify the component about the connection failure
        if (callbacks.connectionFailed) {
          callbacks.connectionFailed(error)
        }
        return null
      }
    }

    if (!this.consumer) {
      console.error('[Cable] Cannot subscribe - no connection available')
      return null
    }

    const key = `${channelName}-${JSON.stringify(channelParams)}`
    
    // Check if already subscribed
    if (this.subscriptions.has(key)) {
      console.log(`[Cable] Already subscribed to ${channelName}`)
      return this.subscriptions.get(key)
    }

    // Store subscription details for reconnection
    this.subscriptionSnapshots.set(key, {
      channelName,
      channelParams,
      callbacks
    })

    const subscription = this.consumer.subscriptions.create(
      { channel: channelName, ...channelParams },
      {
        connected: () => {
          console.log(`[Cable] Subscribed to ${channelName}`)
          if (callbacks.connected) callbacks.connected()
        },
        disconnected: () => {
          console.log(`[Cable] Unsubscribed from ${channelName}`)
          if (callbacks.disconnected) callbacks.disconnected()
        },
        received: (data) => {
          console.log(`[Cable] Received data from ${channelName}:`, data)
          if (callbacks.received) callbacks.received(data)
        },
        ...callbacks
      }
    )

    this.subscriptions.set(key, subscription)
    return subscription
  }

  // Unsubscribe from a channel
  unsubscribe(channelName, channelParams = {}) {
    const key = `${channelName}-${JSON.stringify(channelParams)}`
    const subscription = this.subscriptions.get(key)
    
    if (subscription && this.consumer) {
      this.consumer.subscriptions.remove(subscription)
      this.subscriptions.delete(key)
      this.subscriptionSnapshots.delete(key) // Also remove from snapshots
      console.log(`[Cable] Unsubscribed from ${channelName}`)
    }
  }

  // Check if consumer is connected
  isConnected() {
    return this.consumer && this.consumer.connection.isOpen()
  }

  // Get the consumer instance
  getConsumer() {
    return this.consumer
  }

  // Take a snapshot of current subscriptions (called before disconnect)
  takeSubscriptionSnapshot() {
    // The subscriptionSnapshots map already contains all subscription details
    // maintained during subscribe() calls, so we don't need to do anything here
    console.log(`[Cable] Subscription snapshot maintained: ${this.subscriptionSnapshots.size} channels`)
  }

  // Restore subscriptions after reconnection
  async restoreSubscriptions() {
    if (this.subscriptionSnapshots.size === 0) {
      console.log('[Cable] No subscriptions to restore')
      return
    }

    console.log(`[Cable] Restoring ${this.subscriptionSnapshots.size} subscriptions...`)
    
    // Create a copy of snapshots to iterate over
    const snapshotsToRestore = new Map(this.subscriptionSnapshots)
    
    // Clear current snapshots to avoid duplicates
    this.subscriptionSnapshots.clear()
    
    // Restore each subscription
    for (const [key, snapshot] of snapshotsToRestore) {
      const { channelName, channelParams, callbacks } = snapshot
      try {
        await this.subscribe(channelName, channelParams, callbacks)
        console.log(`[Cable] Restored subscription to ${channelName}`)
      } catch (error) {
        console.error(`[Cable] Failed to restore subscription to ${channelName}:`, error)
      }
    }
    
    console.log('[Cable] Subscription restoration complete')
  }
}

// Export a singleton instance
export const cable = new CableService()

// Re-export the cable instance as default
export default cable