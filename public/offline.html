<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Týmbox</title>
    <style>
        body {
            font-family: Inter, system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
            color: #1e293b;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
        }
        
        .offline-container {
            max-width: 400px;
            background: white;
            border-radius: 8px;
            padding: 40px 30px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .offline-icon {
            width: 64px;
            height: 64px;
            background: #3b82f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        h1 {
            margin: 0 0 16px;
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
        }
        
        p {
            margin: 0 0 24px;
            color: #64748b;
            line-height: 1.5;
        }
        
        .retry-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .retry-button:hover {
            background: #2563eb;
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .features h3 {
            margin: 0 0 12px;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .features ul {
            margin: 0;
            padding-left: 20px;
            color: #64748b;
        }
        
        .features li {
            margin-bottom: 6px;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">T</div>
        <h1>Jste offline</h1>
        <p>Připojení k internetu není k dispozici. Týmbox funguje i offline s omezenými funkcemi.</p>
        
        <button class="retry-button" onclick="window.location.reload()">
            Zkusit znovu
        </button>
        
        <div class="features">
            <h3>Dostupné offline:</h3>
            <ul>
                <li>Prohlížení uložených dat</li>
                <li>Základní navigace</li>
                <li>Lokální úpravy (budou synchronizovány)</li>
            </ul>
        </div>
    </div>

    <script>
        // Check for network connectivity
        function checkOnline() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }
        
        // Listen for online events
        window.addEventListener('online', checkOnline);
        
        // Check every 5 seconds
        setInterval(checkOnline, 5000);
        
        // Show connection status
        window.addEventListener('load', () => {
            if (navigator.onLine) {
                console.log('Network available - redirecting...');
                setTimeout(checkOnline, 1000);
            }
        });
    </script>
</body>
</html>