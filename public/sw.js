// ABOUTME: PWA Service Worker - Phase 1 & 2 implementation with JWT support and offline capabilities
// ABOUTME: Handles service worker registration, caching strategies, and offline API request queuing

const CACHE_NAME = 'tymbox-v1';
const STATIC_CACHE_NAME = 'tymbox-static-v1';
const API_CACHE_NAME = 'tymbox-api-v1';

// Assets to cache for offline use
const STATIC_ASSETS = [
  '/',
  '/offline.html',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// API endpoints that can be cached
const CACHEABLE_API_ENDPOINTS = [
  '/api/v1/user',
  '/api/v1/subscription_status'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[SW] Install event');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[SW] Failed to cache static assets:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activate event');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== API_CACHE_NAME) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Cache cleanup complete');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }
  
  // Handle static assets
  if (request.destination === 'document' || 
      request.destination === 'script' || 
      request.destination === 'style' ||
      request.destination === 'image') {
    event.respondWith(handleStaticRequest(request));
    return;
  }
  
  // Default: try network first
  event.respondWith(
    fetch(request).catch(() => {
      return caches.match('/offline.html');
    })
  );
});

// Handle API requests with JWT validation and caching
async function handleApiRequest(request) {
  try {
    // Extract JWT token from request
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');
    
    // Validate JWT token if present
    if (token && !isValidJWT(token)) {
      console.log('[SW] Invalid JWT token detected');
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Try network first
    const networkResponse = await fetch(request.clone());
    
    // Cache successful responses for cacheable endpoints
    if (networkResponse.ok && isCacheableApiEndpoint(request.url)) {
      const cache = await caches.open(API_CACHE_NAME);
      cache.put(request.clone(), networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('[SW] Network failed for API request:', error);
    
    // Try to serve from cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      console.log('[SW] Serving API request from cache');
      return cachedResponse;
    }
    
    // Queue request for later if offline
    await queueOfflineRequest(request);
    
    return new Response(JSON.stringify({ 
      error: 'Network unavailable', 
      queued: true 
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle static asset requests
async function handleStaticRequest(request) {
  try {
    // Try cache first for static assets
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Try network
    const networkResponse = await fetch(request);
    
    // Cache the response
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request.clone(), networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('[SW] Failed to fetch static asset:', error);
    
    // Try cache again
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page for document requests
    if (request.destination === 'document') {
      return caches.match('/offline.html');
    }
    
    return new Response('Network Error', { status: 503 });
  }
}

// Basic JWT token validation
function isValidJWT(token) {
  try {
    // Simple JWT structure validation
    const parts = token.split('.');
    if (parts.length !== 3) {
      return false;
    }
    
    // Decode payload to check expiration
    const payload = JSON.parse(atob(parts[1]));
    const now = Math.floor(Date.now() / 1000);
    
    // Check if token is expired
    if (payload.exp && payload.exp < now) {
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('[SW] JWT validation error:', error);
    return false;
  }
}

// Check if API endpoint should be cached
function isCacheableApiEndpoint(url) {
  return CACHEABLE_API_ENDPOINTS.some(endpoint => url.includes(endpoint));
}

// Queue offline requests for background sync
async function queueOfflineRequest(request) {
  try {
    // Simple offline queue using IndexedDB would go here
    // For Phase 2, we'll implement a basic approach
    console.log('[SW] Queuing offline request:', request.url);
    
    // Store request details in IndexedDB or localStorage
    const requestData = {
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      body: await request.text(),
      timestamp: Date.now()
    };
    
    // In a real implementation, store in IndexedDB
    // For now, just log it
    console.log('[SW] Request queued:', requestData);
    
  } catch (error) {
    console.error('[SW] Failed to queue offline request:', error);
  }
}

// Message handling for client communication
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});

console.log('[SW] Service Worker loaded successfully');