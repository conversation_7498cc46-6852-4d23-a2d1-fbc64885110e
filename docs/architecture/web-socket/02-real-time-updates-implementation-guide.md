# Real-Time Updates Implementation Guide

## Overview

This document provides a comprehensive guide for implementing real-time updates using Rails Action Cable and Vue.js WebSocket connections. This is an "evergreen" guide designed to help future developers and AI agents avoid common pitfalls and debugging cycles.

## Architecture Components

### Backend (Rails Action Cable)

#### 1. Action Cable Configuration

**File**: `config/environments/development.rb`
```ruby
# Allow Action Cable connections from Vue.js dev server on local network
config.action_cable.allowed_request_origins = [
  /http:\/\/localhost:\d+/,
  /http:\/\/127\.0\.0\.1:\d+/,
  /http:\/\/192\.168\.\d+\.\d+:\d+/
]

# Disable request forgery protection for development
config.action_cable.disable_request_forgery_protection = true
```

**File**: `config/cable.yml`
```yaml
development:
  adapter: redis
  url: <%= ENV.fetch("REDIS_URL", "redis://localhost:6379/1") %>
```

#### 2. Routes Configuration

**File**: `config/routes.rb`
```ruby
# Action Cable endpoint - MUST be before catch-all routes
mount ActionCable.server => '/cable'

# Catch-all route MUST exclude /cable paths
get '*path', to: 'spa#index', constraints: lambda { |req|
  !req.xhr? && 
  req.format.html? && 
  !req.path.start_with?('/api') &&
  !req.path.start_with?('/rails') &&
  !req.path.start_with?('/assets') &&
  !req.path.start_with?('/vite') &&
  !req.path.start_with?('/cable') &&  # CRITICAL: Exclude cable paths
  !req.path.match(/^\/r\//) &&
  !req.path.match(/^\/private_meetings\//)
}
```

#### 3. Channel Implementation

**File**: `app/channels/application_cable/connection.rb`
```ruby
module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user, :current_tenant

    def connect
      self.current_user = find_verified_user
      self.current_tenant = set_tenant_from_jwt
      logger.add_tags 'ActionCable', current_user.email, "Company:#{current_tenant&.id}"
    end

    private

    def find_verified_user
      # Extract JWT token from query parameters
      token = request.params[:token]
      return reject_unauthorized_connection unless token

      payload = JwtService.decode(token)
      return reject_unauthorized_connection unless payload

      User.find_by(id: payload['user_id']) || reject_unauthorized_connection
    end
  end
end
```

**File**: `app/channels/team_status_channel.rb`
```ruby
class TeamStatusChannel < ApplicationCable::Channel
  def subscribed
    if current_tenant && can_view_team_status?
      stream_for current_tenant
      logger.info "[TeamStatusChannel] User #{current_user.email} subscribed"
    else
      reject
    end
  end

  private

  def can_view_team_status?
    # Check permissions based on your authorization system
    current_user.has_role?(:owner, current_tenant) ||
    current_user.has_role?(:supervisor, current_tenant) ||
    current_user.has_role?(:administrator, current_tenant)
  end
end
```

#### 4. Broadcasting from Controllers

**Critical Point: ID Consistency**
```ruby
def broadcast_team_status_update(employee:, working:, work:, activity:, event_type:)
  broadcast_data = {
    type: 'team_status_update',
    event_type: event_type,
    # CRITICAL: Use the SAME ID type that frontend expects
    employee_id: @contract.id, # NOT employee.id if frontend expects contract_id
    employee_name: employee_name,
    working: working,
    # ... other data
  }

  TeamStatusChannel.broadcast_to(@company, broadcast_data)
rescue => e
  Rails.logger.error "[TeamStatusChannel] Failed to broadcast: #{e.message}"
  # Don't let broadcast failures affect the main action
end
```

### Frontend (Vue.js + Action Cable)

#### 1. Cable Service Implementation

**File**: `app/frontend/services/cable.js`
```javascript
import { createConsumer } from '@rails/actioncable'
import store from '../store'

class CableService {
  constructor() {
    this.consumer = null
    this.subscriptions = new Map()
    this.connectionPromise = null
  }

  connect() {
    if (this.consumer && this.consumer.connection.isOpen()) {
      return Promise.resolve(this.consumer)
    }

    if (this.connectionPromise) {
      return this.connectionPromise
    }

    this.connectionPromise = new Promise((resolve, reject) => {
      const token = store.getters['userStore/jwtToken']
      if (!token) {
        reject(new Error('No JWT token available'))
        return
      }

      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.host
      const cableUrl = `${protocol}//${host}/cable?token=${encodeURIComponent(token)}`

      this.consumer = createConsumer(cableUrl)

      // Use correct Action Cable events
      this.consumer.connection.events.open = () => {
        resolve(this.consumer)
      }

      this.consumer.connection.events.error = (error) => {
        this.consumer = null
        reject(new Error('Connection error: ' + error))
      }

      // CRITICAL: Explicitly open the connection
      this.consumer.connection.open()

      setTimeout(() => {
        this.consumer = null
        reject(new Error('Connection timeout'))
      }, 30000)
    }).finally(() => {
      this.connectionPromise = null
    })

    return this.connectionPromise
  }

  async subscribe(channelName, channelParams = {}, callbacks = {}) {
    await this.connect()
    
    const key = `${channelName}-${JSON.stringify(channelParams)}`
    if (this.subscriptions.has(key)) {
      return this.subscriptions.get(key)
    }

    const subscription = this.consumer.subscriptions.create(
      { channel: channelName, ...channelParams },
      callbacks
    )

    this.subscriptions.set(key, subscription)
    return subscription
  }
}

export const cable = new CableService()
export default cable
```

#### 2. Vue Component Integration

**File**: `app/frontend/components/Mainbox.vue`
```javascript
import cable from '../services/cable'

export default {
  methods: {
    async subscribeToTeamStatus() {
      try {
        this.teamStatusSubscription = await cable.subscribe('TeamStatusChannel', {}, {
          connected: () => {
            console.log('[Mainbox] Connected to TeamStatusChannel')
          },
          received: (data) => {
            console.log('[Mainbox] Received team status update:', data)
            this.handleTeamStatusUpdate(data)
          }
        })
      } catch (error) {
        console.error('[Mainbox] Failed to subscribe:', error)
      }
    }
  },

  mounted() {
    this.subscribeToTeamStatus()
  }
}
```

#### 3. Vuex Store Integration

**File**: `app/frontend/store/ownerStore.js`
```javascript
export default {
  mutations: {
    UPDATE_EMPLOYEE_STATUS(state, { employeeId, updates }) {
      // CRITICAL: Handle type conversion for ID matching
      const employee = state.employees.find(emp => emp.id === String(employeeId))
      
      if (employee) {
        Object.assign(employee, updates)
        // Force reactivity
        state.employees = [...state.employees]
      }
    }
  },

  actions: {
    handleTeamStatusUpdate({ commit }, data) {
      switch (data.event_type) {
        case 'work_activity_started':
          commit('UPDATE_EMPLOYEE_STATUS', {
            employeeId: data.employee_id,
            updates: {
              working: true,
              current_work: data.current_work,
              activityStartedAt: data.activity_started_at
            }
          })
          break
          
        case 'work_activity_ended':
          commit('UPDATE_EMPLOYEE_STATUS', {
            employeeId: data.employee_id,
            updates: {
              working: false,
              current_work: null
            }
          })
          break
      }
    }
  }
}
```

## Common Pitfalls and Solutions

### 1. **Route Configuration Issues**

**Problem**: WebSocket connections fail with 404 errors
**Cause**: Catch-all SPA routes intercept `/cable` requests
**Solution**: Ensure Action Cable mount comes before catch-all routes and exclude `/cable` from catch-all constraints

### 2. **Origin Validation Failures**

**Problem**: `Request origin not allowed` errors
**Cause**: Action Cable rejects connections from non-whitelisted origins
**Solution**: Configure `allowed_request_origins` for your development/production environments

### 3. **Connection Not Opening**

**Problem**: Consumer created but no WebSocket connection attempted
**Cause**: Action Cable requires explicit connection opening
**Solution**: Call `this.consumer.connection.open()` after setting up event listeners

### 4. **ID Type Mismatches**

**Problem**: Broadcasts received but frontend can't find matching records
**Cause**: Backend sends numbers, frontend expects strings (or vice versa)
**Solution**: Ensure consistent ID types or convert with `String(id)` in comparisons

### 5. **Authentication Failures**

**Problem**: JWT token not passed to WebSocket connection
**Cause**: WebSocket doesn't support Authorization headers reliably
**Solution**: Pass JWT token via query parameters: `/cable?token=${token}`

### 6. **Missing Error Handling**

**Problem**: Silent failures make debugging difficult
**Cause**: Insufficient logging and error handling
**Solution**: Add comprehensive logging at all levels (connection, subscription, broadcast)

## Testing Checklist

### Backend Tests
- [ ] Action Cable mount point responds to WebSocket upgrade requests
- [ ] JWT authentication works via query parameters
- [ ] Channels properly authorize users based on roles
- [ ] Broadcasts are sent with correct data structure and ID types
- [ ] Error handling prevents broadcast failures from affecting main actions

### Frontend Tests
- [ ] Cable service establishes WebSocket connection
- [ ] Components successfully subscribe to channels
- [ ] Vuex store processes incoming data correctly
- [ ] UI updates reflect real-time changes
- [ ] Connection failures are handled gracefully

### Integration Tests
- [ ] End-to-end real-time updates work across different user accounts
- [ ] Broadcasting triggers immediate UI updates without page refresh
- [ ] Connection survives network interruptions and reconnects
- [ ] Multiple subscribers receive the same broadcasts

## Performance Considerations

1. **Connection Management**: Reuse WebSocket connections across components
2. **Subscription Cleanup**: Unsubscribe when components unmount
3. **Broadcast Frequency**: Avoid excessive broadcasting on high-frequency events
4. **Data Payload Size**: Keep broadcast payloads minimal
5. **Redis Configuration**: Tune Redis for Action Cable workload

## Security Considerations

1. **JWT Token Validation**: Always validate JWT tokens in Action Cable connections
2. **Authorization**: Check user permissions before allowing channel subscriptions
3. **Data Filtering**: Only broadcast data the user is authorized to see
4. **Rate Limiting**: Consider implementing rate limiting for WebSocket connections
5. **Origin Validation**: Properly configure allowed request origins

## Debugging Tips

1. **Enable Debug Logging**: Add comprehensive console.log statements during development
2. **Monitor Network Tab**: Check for WebSocket connection attempts and failures
3. **Check Rails Logs**: Look for Action Cable connection and broadcast messages
4. **Verify JWT Tokens**: Ensure tokens are valid and contain required claims
5. **Test with curl**: Use curl to test WebSocket upgrade requests manually

## Conclusion

Real-time updates require careful coordination between backend broadcasting, WebSocket infrastructure, and frontend state management. The key to success is:

1. **Consistent ID management** across backend and frontend
2. **Proper route configuration** to avoid conflicts
3. **Comprehensive error handling** and logging
4. **Explicit connection management** in JavaScript
5. **Type-safe data handling** in state mutations

Following this guide should eliminate common debugging cycles and provide a solid foundation for implementing real-time features.