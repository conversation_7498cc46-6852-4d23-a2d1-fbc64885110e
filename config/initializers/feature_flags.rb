# frozen_string_literal: true

# Feature flags for controlling JWT authentication rollout
# These can be toggled without deploying new code
module FeatureFlags
  class << self
    # JWT Feature Flags
    def jwt_enabled?
      #ENV.fetch('JWT_ENABLED', 'false') == 'true'
      true
    end

    def jwt_login_enabled?
      #jwt_enabled? && ENV.fetch('JWT_LOGIN_ENABLED', 'false') == 'true'
      true
    end

    def jwt_api_enabled?
      #jwt_enabled? && ENV.fetch('JWT_API_ENABLED', 'false') == 'true'
      true
    end

    def session_auth_disabled?
      #ENV.fetch('SESSION_AUTH_DISABLED', 'false') == 'true'
      true
    end

    def dual_auth_enabled?
      jwt_api_enabled? && !session_auth_disabled?
    end

    # CHUNK 37 IMPLEMENTATION: JWT-only mode feature flag for API authentication
    # When enabled, API endpoints require JWT authentication only (no session fallback)
    # See: docs/jwt_implementation_notes_chunk_37.md for implementation details
    def jwt_only_mode_enabled?
      #ENV.fetch('JWT_ONLY_MODE_ENABLED', 'false') == 'true'
      true
    end

    # PWA Feature Flags - Manual toggle approach for Phase 1 & 2
    def pwa_enabled?
      # Manual toggle for PWA Phase 1 & 2 implementation
      # TODO: Switch to ENV.fetch('PWA_ENABLED', 'false') == 'true' after Phase 6
      true
    end

    def service_worker_enabled?
      # Manual toggle for service worker functionality
      # TODO: Switch to ENV-based after Phase 6
      pwa_enabled? && true
    end

    def offline_mode_enabled?
      # Manual toggle for offline mode (Phase 2 feature)
      # TODO: Switch to ENV-based after Phase 6  
      pwa_enabled? && true
    end

    # Debug and Monitoring Flags
    def auth_debug_enabled?
      ENV.fetch('AUTH_DEBUG_ENABLED', 'false') == 'true'
    end

    def auth_monitoring_enabled?
      ENV.fetch('AUTH_MONITORING_ENABLED', 'true') == 'true'
    end

    # Rollback flags
    def emergency_rollback_enabled?
      ENV.fetch('EMERGENCY_ROLLBACK', 'false') == 'true'
    end

    # Get all feature flags status
    def status
      {
        jwt: {
          enabled: jwt_enabled?,
          login_enabled: jwt_login_enabled?,
          api_enabled: jwt_api_enabled?,
          dual_auth: dual_auth_enabled?,
          only_mode: jwt_only_mode_enabled?
        },
        session: {
          disabled: session_auth_disabled?
        },
        pwa: {
          enabled: pwa_enabled?,
          service_worker: service_worker_enabled?,
          offline_mode: offline_mode_enabled?
        },
        debug: {
          auth_debug: auth_debug_enabled?,
          auth_monitoring: auth_monitoring_enabled?
        },
        emergency: {
          rollback_enabled: emergency_rollback_enabled?
        }
      }
    end

    # Log feature flag status
    def log_status
      Rails.logger.info "[FeatureFlags] Current status: #{status.to_json}"
    end
  end
end

# Log feature flags on startup
Rails.application.config.after_initialize do
  FeatureFlags.log_status if Rails.env.development? || Rails.env.staging?
end