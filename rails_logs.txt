=> Booting Puma
=> Rails 7.0.8.6 application starting in development 
=> Run `bin/rails server --help` for more startup options
Puma starting in single mode...
* Puma version: 5.6.9 (ruby 3.1.2-p20) ("<PERSON><PERSON>'s Version")
*  Min threads: 5
*  Max threads: 5
*  Environment: development
*          PID: 336245
* Listening on http://127.0.0.1:3000
* Listening on http://[::1]:3000
Use Ctrl-C to stop
Started GET "/api/v1/dashboard" for ::1 at 2025-07-26 07:32:08 +0200
  [1m[36mActiveRecord::SchemaMigration Pluck (1.8ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
[SECURITY] Rack::Attack blocked request: safelist - ::1 - GET /api/v1/dashboard
[SECURITY] rate_limit_violation: {:ip=>"::1", :path=>"/api/v1/dashboard", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-localhost"}
  
ActionController::RoutingError (No route matches [GET] "/api/v1/dashboard"):
  
