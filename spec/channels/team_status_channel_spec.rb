# ABOUTME: Tests for TeamStatusChannel real-time updates functionality
# ABOUTME: Validates role-based access and broadcasting for team status changes

require 'rails_helper'

RSpec.describe TeamStatusChannel, type: :channel do
  let(:company) { create(:company) }
  let(:owner_user) { create(:user) }
  let(:employee_user) { create(:user) }
  let(:owner_contract) { create(:contract, user: owner_user, company: company) }
  let(:employee_contract) { create(:contract, user: employee_user, company: company) }
  
  before do
    ActsAsTenant.current_tenant = company
    create(:company_user_role, user: owner_user, company: company, role: create(:role, name: 'owner'))
    create(:company_user_role, user: employee_user, company: company, role: create(:role, name: 'employee'))
  end

  describe 'subscription' do
    context 'when user is a manager (owner)' do
      before do
        stub_connection(current_user: owner_user, current_tenant: company)
      end

      it 'successfully subscribes to the channel' do
        subscribe
        expect(subscription).to be_confirmed
        expect(subscription).to have_stream_for(company)
      end
    end

    context 'when user is a regular employee' do
      before do
        stub_connection(current_user: employee_user, current_tenant: company)
      end

      it 'rejects the subscription' do
        subscribe
        expect(subscription).to be_rejected
      end
    end

    context 'when no tenant is set' do
      before do
        stub_connection(current_user: owner_user, current_tenant: nil)
      end

      it 'rejects the subscription' do
        subscribe
        expect(subscription).to be_rejected
      end
    end
  end

  describe 'broadcasting' do
    it 'broadcasts team status updates to the company channel' do
      expect {
        TeamStatusChannel.broadcast_to(
          company,
          {
            type: 'team_status_update',
            event_type: 'work_activity_started',
            employee_id: employee_user.id,
            employee_name: "#{employee_contract.first_name} #{employee_contract.last_name}",
            working: true,
            current_work: {
              id: 1,
              title: 'Test Work',
              location: 'Test Location',
              client_name: nil
            },
            activity_started_at: Time.current.iso8601
          }
        )
      }.to have_broadcasted_to(company).from_channel(TeamStatusChannel)
    end
  end
end