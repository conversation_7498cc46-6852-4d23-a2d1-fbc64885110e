API authenticated via JWT for user: 2
  [1m[36mContract Load (2.5ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/events_controller.rb:180:in `set_contract'
[🔍 DEBUG] EventsController#index called
[🔍 DEBUG] Current user: <EMAIL>
[🔍 DEBUG] Current company: 2 - Claude Company
[🔍 DEBUG] Contract: 1
  [1m[36mEvent Count (1.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "events" INNER JOIN "contracts" ON "events"."contract_id" = "contracts"."id" WHERE "events"."company_id" = $1 AND "contracts"."company_id" = $2[0m  [["company_id", 2], ["company_id", 2]]
  ↳ app/controllers/api/v1/events_controller.rb:29:in `index'
[🔍 DEBUG] Found 3 total events for company
  [1m[36mEvent Load (1.8ms)[0m  [1m[34mSELECT "events".* FROM "events" WHERE "events"."company_id" = $1 AND "events"."user_id" = $2 AND "events"."contract_id" = $3 AND "events"."status" = $4 AND (event_type < 5) AND "events"."start_time" BETWEEN $5 AND $6 ORDER BY "events"."id" ASC LIMIT $7[0m  [["company_id", 2], ["user_id", 2], ["contract_id", 1], ["status", "approved"], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:198:in `current_status'
  [1m[36mEvent Load (7.0ms)[0m  [1m[34mSELECT "events".* FROM "events" INNER JOIN "contracts" ON "events"."contract_id" = "contracts"."id" WHERE "events"."company_id" = $1 AND "contracts"."company_id" = $2 AND "events"."status" = $3[0m  [["company_id", 2], ["company_id", 2], ["status", "pending"]]
  ↳ app/controllers/api/v1/events_controller.rb:31:in `map'
[🔍 DEBUG] Returning 0 events with status 'pending'
[🔍 DEBUG] Sample event: 
[AuthMetrics] JWT request tracked for user: 2
Completed 200 OK in 199ms (Views: 0.4ms | ActiveRecord: 16.8ms | Allocations: 85433)


Started GET "/api/v1/employees" for ************ at 2025-07-26 04:33:04 +0200
[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /api/v1/works/assigned
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/api/v1/works/assigned", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
Processing by Api::V1::WorksController#assigned as JSON
No JWT tenant context available
  [1m[36mUser Load (2.1ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mBreak Load (2.5ms)[0m  [1m[34mSELECT "breaks".* FROM "breaks" WHERE "breaks"."user_id" = $1 AND "breaks"."contract_id" = $2 AND "breaks"."start_time" BETWEEN $3 AND $4 ORDER BY "breaks"."id" ASC LIMIT $5[0m  [["user_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:204:in `current_status'
  [1m[36mCompany Load (6.8ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (0.8ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (0.7ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /daily_activities?date=2025-07-26
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/daily_activities?date=2025-07-26", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
API authenticated via JWT for user: 2
  [1m[36mContract Load (2.3ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/works_controller.rb:232:in `set_contract'
Processing by DailyActivitiesController#index as JSON
  Parameters: {"date"=>"2025-07-26"}
  [1m[36mCompanyUserRole Load (8.3ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."company_id" = $3 LIMIT $4[0m  [["active", true], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
  [1m[36mCACHE Role Load (0.0ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
  [1m[36mUser Load (1.1ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mCompany Load (0.7ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (1.6ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (7.0ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
  [1m[36mUserSetting Load (1.4ms)[0m  [1m[34mSELECT "user_settings".* FROM "user_settings" WHERE "user_settings"."user_id" = $1 LIMIT $2[0m  [["user_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:215:in `current_status'
JWT tenant context set: company_id=2 for user=2 with role=owner
Started GET "/company_connections/fetch" for ************ at 2025-07-26 04:33:05 +0200
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
Rails controller JWT authentication successful for user: 2
  [1m[36mDailyActivity Load (2.0ms)[0m  [1m[34mSELECT "daily_activities".* FROM "daily_activities" WHERE "daily_activities"."user_id" = $1 AND "daily_activities"."company_id" = $2 AND "daily_activities"."contract_id" = $3 AND "daily_activities"."start_time" BETWEEN $4 AND $5 ORDER BY "daily_activities"."created_at" DESC[0m  [["user_id", 2], ["company_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"]]
  ↳ app/controllers/daily_logs_controller.rb:217:in `current_status'
Rails controller authenticated via JWT for user: 2
Using JWT tenant context: company_id=2
  [1m[36mContract Load (26.7ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/daily_activities_controller.rb:225:in `set_contract'
  [1m[36mDailyLog Load (6.7ms)[0m  [1m[34mSELECT "daily_logs".* FROM "daily_logs" WHERE "daily_logs"."company_id" = $1 AND "daily_logs"."id" = $2[0m  [["company_id", 2], ["id", 5]]
  [1m[36mDailyActivity Load (5.3ms)[0m  [1m[34mSELECT "daily_activities".* FROM "daily_activities" WHERE "daily_activities"."user_id" = $1 AND "daily_activities"."company_id" = $2 AND "daily_activities"."contract_id" = $3 AND "daily_activities"."start_time" BETWEEN $4 AND $5 ORDER BY "daily_activities"."created_at" DESC[0m  [["user_id", 2], ["company_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"]]
  ↳ app/controllers/daily_activities_controller.rb:36:in `index'
  ↳ app/controllers/daily_logs_controller.rb:217:in `current_status'
Completed 200 OK in 1038ms (Views: 69.0ms | ActiveRecord: 101.9ms | Allocations: 370605)


user: mm
GET /daily_logs/current_status
AVOID eager loading detected
  DailyActivity => [:daily_log]
  Remove from your query: .includes([:daily_log])
Call stack
  /home/<USER>/Projects/attendifyapp/app/controllers/daily_logs_controller.rb:217:in `current_status'
  /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `block in use_locale_from_cookie_for_api'
  /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `use_locale_from_cookie_for_api'


  [1m[36mWork Count (1.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "works" WHERE "works"."company_id" = $1 AND "works"."id" IN (SELECT "work_assignments"."work_id" FROM "work_assignments" WHERE "work_assignments"."company_id" = $2 AND "work_assignments"."contract_id" = $3)[0m  [["company_id", 2], ["company_id", 2], ["contract_id", 1]]
  ↳ app/controllers/api/v1/works_controller.rb:47:in `assigned'
  [1m[36mWork Load (2.9ms)[0m  [1m[34mSELECT "works".* FROM "works" WHERE "works"."company_id" = $1 AND "works"."id" IN (SELECT "work_assignments"."work_id" FROM "work_assignments" WHERE "work_assignments"."company_id" = $2 AND "work_assignments"."contract_id" = $3) ORDER BY "works"."created_at" DESC[0m  [["company_id", 2], ["company_id", 2], ["contract_id", 1]]
  ↳ app/controllers/api/v1/works_controller.rb:50:in `assigned'
  [1m[36mWork Load (2.8ms)[0m  [1m[34mSELECT "works".* FROM "works" WHERE "works"."company_id" = $1 AND "works"."id" = $2[0m  [["company_id", 2], ["id", 3]]
  ↳ app/controllers/daily_activities_controller.rb:36:in `index'
Completed 200 OK in 296ms (Views: 3.3ms | ActiveRecord: 50.0ms | Allocations: 50175)


  [1m[36mWorkAssignment Load (16.8ms)[0m  [1m[34mSELECT "work_assignments".* FROM "work_assignments" WHERE "work_assignments"."company_id" = $1 AND "work_assignments"."work_id" = $2[0m  [["company_id", 2], ["work_id", 3]]
  ↳ app/controllers/api/v1/works_controller.rb:50:in `assigned'
  [1m[36mContract Load (1.4ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."id" IN ($2, $3)[0m  [["company_id", 2], ["id", 1], ["id", 6]]
  ↳ app/controllers/api/v1/works_controller.rb:50:in `assigned'
  [1m[36mWorkAssignment Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "work_assignments" WHERE "work_assignments"."company_id" = $1 AND "work_assignments"."work_id" = $2[0m  [["company_id", 2], ["work_id", 3]]
  ↳ app/controllers/api/v1/works_controller.rb:51:in `block in assigned'
[AuthMetrics] JWT request tracked for user: 2
Completed 200 OK in 471ms (Views: 0.5ms | ActiveRecord: 59.7ms | Allocations: 108247)


Started GET "/bookings/fetch?status=pending" for ************ at 2025-07-26 04:33:05 +0200
[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /api/v1/employees
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/api/v1/employees", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
Processing by Api::V1::EmployeesController#index as JSON
No JWT tenant context available
  [1m[36mUser Load (0.8ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mCompany Load (0.7ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (0.5ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (0.5ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
API authenticated via JWT for user: 2
  [1m[36mContract Load (0.9ms)[0m  [1m[34mSELECT "contracts"."id", "contracts"."first_name", "contracts"."last_name", "contracts"."phone", "latest_logs"."start_time", "latest_logs"."end_time" FROM "contracts" LEFT JOIN (
            SELECT DISTINCT ON (contract_id) contract_id, start_time, end_time
            FROM daily_logs
            ORDER BY contract_id, start_time DESC
          ) latest_logs ON latest_logs.contract_id = contracts.id WHERE "contracts"."company_id" = $1 AND "contracts"."status" = $2 AND "contracts"."user_id" IS NOT NULL ORDER BY latest_logs.start_time DESC NULLS LAST[0m  [["company_id", 2], ["status", 0]]
  ↳ app/controllers/api/v1/employees_controller.rb:48:in `map'
  [1m[36mDailyActivity Load (0.8ms)[0m  [1m[34mSELECT daily_activities.*, works.title as work_title, works.location as work_location FROM "daily_activities" INNER JOIN "works" ON "works"."company_id" = $1 AND "works"."id" = "daily_activities"."work_id" WHERE "daily_activities"."contract_id" = $2 AND "daily_activities"."end_time" IS NULL AND "daily_activities"."activity_type" IN ($3, $4, $5) ORDER BY "daily_activities"."id" ASC LIMIT $6[0m  [["company_id", 2], ["contract_id", 6], ["activity_type", "work_at_location"], ["activity_type", "work_remote"], ["activity_type", "travel_to_work"], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/employees_controller.rb:58:in `block in index'
Started GET "/api/v1/works/today" for ************ at 2025-07-26 04:33:05 +0200
  [1m[36mDailyActivity Load (1.6ms)[0m  [1m[34mSELECT daily_activities.*, works.title as work_title, works.location as work_location FROM "daily_activities" INNER JOIN "works" ON "works"."company_id" = $1 AND "works"."id" = "daily_activities"."work_id" WHERE "daily_activities"."contract_id" = $2 AND "daily_activities"."end_time" IS NULL AND "daily_activities"."activity_type" IN ($3, $4, $5) ORDER BY "daily_activities"."id" ASC LIMIT $6[0m  [["company_id", 2], ["contract_id", 1], ["activity_type", "work_at_location"], ["activity_type", "work_remote"], ["activity_type", "travel_to_work"], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/employees_controller.rb:58:in `block in index'
[AuthMetrics] JWT request tracked for user: 2
Completed 200 OK in 43ms (Views: 0.6ms | ActiveRecord: 5.7ms | Allocations: 13510)


[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /company_connections/fetch
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/company_connections/fetch", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
Processing by CompanyConnectionsController#fetch as JSON
  [1m[36mUser Load (1.1ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
Started GET "/api/v1/works/assigned" for ************ at 2025-07-26 04:33:05 +0200
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mCompany Load (0.6ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (1.6ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (1.1ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
Rails controller JWT authentication successful for user: 2
Rails controller authenticated via JWT for user: 2
Using JWT tenant context: company_id=2
  [1m[36mContract Load (2.4ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."email" = $1 AND "contracts"."user_id" IS NULL[0m  [["email", "<EMAIL>"]]
  ↳ app/controllers/company_connections_controller.rb:13:in `fetch'
Completed 200 OK in 54ms (Views: 0.4ms | ActiveRecord: 6.7ms | Allocations: 9423)


[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /bookings/fetch?status=pending
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/bookings/fetch?status=pending", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
Processing by BookingsController#fetch as JSON
  Parameters: {"status"=>"pending"}
  [1m[36mUser Load (0.7ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mCompany Load (0.8ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (0.8ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (0.9ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
Rails controller JWT authentication successful for user: 2
Rails controller authenticated via JWT for user: 2
Using JWT tenant context: company_id=2
  [1m[36mCompanyUserRole Load (0.9ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."company_id" = $3 LIMIT $4[0m  [["active", true], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
  [1m[36mCACHE Role Load (0.0ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
  [1m[36mBooking Load (0.9ms)[0m  [1m[34mSELECT "bookings".* FROM "bookings" WHERE "bookings"."company_id" = $1 AND "bookings"."status" = $2 ORDER BY "bookings"."created_at" DESC[0m  [["company_id", 2], ["status", "pending"]]
  ↳ app/controllers/bookings_controller.rb:20:in `fetch'
Completed 200 OK in 88ms (Views: 0.5ms | ActiveRecord: 10.0ms | Allocations: 15777)


[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /api/v1/works/today
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/api/v1/works/today", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
Processing by Api::V1::WorksController#today as JSON
No JWT tenant context available
  [1m[36mUser Load (0.6ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mCompany Load (0.5ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (1.2ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (0.6ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
API authenticated via JWT for user: 2
  [1m[36mContract Load (0.7ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/works_controller.rb:232:in `set_contract'
  [1m[36mCompanyUserRole Load (0.6ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."company_id" = $3 LIMIT $4[0m  [["active", true], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
  [1m[36mCACHE Role Load (0.0ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /api/v1/works/assigned
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/api/v1/works/assigned", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
  [1m[36mWork Load (3.9ms)[0m  [1m[34mSELECT "works".* FROM "works" WHERE "works"."company_id" = $1 AND "works"."id" IN (SELECT "work_assignments"."work_id" FROM "work_assignments" WHERE "work_assignments"."company_id" = $2 AND "work_assignments"."contract_id" = $3) AND (scheduled_start_date >= '2025-07-26') AND "works"."status" IN ($4, $5, $6) ORDER BY "works"."scheduled_start_date" ASC, "works"."created_at" ASC[0m  [["company_id", 2], ["company_id", 2], ["contract_id", 1], ["status", "scheduled"], ["status", "in_progress"], ["status", "unprocessed"]]
  ↳ app/controllers/api/v1/works_controller.rb:86:in `today'
Processing by Api::V1::WorksController#assigned as JSON
No JWT tenant context available
[AuthMetrics] JWT request tracked for user: 2
  [1m[36mUser Load (4.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
Completed 200 OK in 92ms (Views: 0.7ms | ActiveRecord: 8.2ms | Allocations: 19353)


  [1m[36mCompany Load (2.1ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (0.9ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (1.4ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
API authenticated via JWT for user: 2
  [1m[36mContract Load (0.6ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/works_controller.rb:232:in `set_contract'
  [1m[36mCompanyUserRole Load (1.8ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."company_id" = $3 LIMIT $4[0m  [["active", true], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
  [1m[36mCACHE Role Load (0.0ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
  [1m[36mWork Count (2.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "works" WHERE "works"."company_id" = $1 AND "works"."id" IN (SELECT "work_assignments"."work_id" FROM "work_assignments" WHERE "work_assignments"."company_id" = $2 AND "work_assignments"."contract_id" = $3)[0m  [["company_id", 2], ["company_id", 2], ["contract_id", 1]]
  ↳ app/controllers/api/v1/works_controller.rb:47:in `assigned'
Started GET "/daily_logs/current_status" for ************ at 2025-07-26 04:33:05 +0200
  [1m[36mWork Load (2.2ms)[0m  [1m[34mSELECT "works".* FROM "works" WHERE "works"."company_id" = $1 AND "works"."id" IN (SELECT "work_assignments"."work_id" FROM "work_assignments" WHERE "work_assignments"."company_id" = $2 AND "work_assignments"."contract_id" = $3) ORDER BY "works"."created_at" DESC[0m  [["company_id", 2], ["company_id", 2], ["contract_id", 1]]
  ↳ app/controllers/api/v1/works_controller.rb:50:in `assigned'
  [1m[36mWorkAssignment Load (0.8ms)[0m  [1m[34mSELECT "work_assignments".* FROM "work_assignments" WHERE "work_assignments"."company_id" = $1 AND "work_assignments"."work_id" = $2[0m  [["company_id", 2], ["work_id", 3]]
  ↳ app/controllers/api/v1/works_controller.rb:50:in `assigned'
  [1m[36mContract Load (1.0ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."id" IN ($2, $3)[0m  [["company_id", 2], ["id", 1], ["id", 6]]
  ↳ app/controllers/api/v1/works_controller.rb:50:in `assigned'
  [1m[36mWorkAssignment Count (0.9ms)[0m  [1m[34mSELECT COUNT(*) FROM "work_assignments" WHERE "work_assignments"."company_id" = $1 AND "work_assignments"."work_id" = $2[0m  [["company_id", 2], ["work_id", 3]]
  ↳ app/controllers/api/v1/works_controller.rb:51:in `block in assigned'
[AuthMetrics] JWT request tracked for user: 2
Completed 200 OK in 127ms (Views: 1.1ms | ActiveRecord: 18.6ms | Allocations: 25668)


[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /daily_logs/current_status
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/daily_logs/current_status", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
Processing by DailyLogsController#current_status as JSON
  [1m[36mUser Load (0.6ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mCompany Load (0.5ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (0.5ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (0.5ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
Rails controller JWT authentication successful for user: 2
Rails controller authenticated via JWT for user: 2
Using JWT tenant context: company_id=2
  [1m[36mContract Load (0.5ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:335:in `set_contract'
  [1m[36mDailyLog Load (0.6ms)[0m  [1m[34mSELECT "daily_logs".* FROM "daily_logs" WHERE "daily_logs"."company_id" = $1 AND "daily_logs"."user_id" = $2 AND "daily_logs"."contract_id" = $3 AND "daily_logs"."start_time" BETWEEN $4 AND $5 ORDER BY "daily_logs"."created_at" DESC LIMIT $6[0m  [["company_id", 2], ["user_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:192:in `current_status'
  [1m[36mEvent Load (1.0ms)[0m  [1m[34mSELECT "events".* FROM "events" WHERE "events"."company_id" = $1 AND "events"."user_id" = $2 AND "events"."contract_id" = $3 AND "events"."status" = $4 AND (event_type < 5) AND "events"."start_time" BETWEEN $5 AND $6 ORDER BY "events"."id" ASC LIMIT $7[0m  [["company_id", 2], ["user_id", 2], ["contract_id", 1], ["status", "approved"], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:198:in `current_status'
  [1m[36mBreak Load (0.5ms)[0m  [1m[34mSELECT "breaks".* FROM "breaks" WHERE "breaks"."user_id" = $1 AND "breaks"."contract_id" = $2 AND "breaks"."start_time" BETWEEN $3 AND $4 ORDER BY "breaks"."id" ASC LIMIT $5[0m  [["user_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:204:in `current_status'
  [1m[36mUserSetting Load (0.4ms)[0m  [1m[34mSELECT "user_settings".* FROM "user_settings" WHERE "user_settings"."user_id" = $1 LIMIT $2[0m  [["user_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:215:in `current_status'
  [1m[36mDailyActivity Load (0.5ms)[0m  [1m[34mSELECT "daily_activities".* FROM "daily_activities" WHERE "daily_activities"."user_id" = $1 AND "daily_activities"."company_id" = $2 AND "daily_activities"."contract_id" = $3 AND "daily_activities"."start_time" BETWEEN $4 AND $5 ORDER BY "daily_activities"."created_at" DESC[0m  [["user_id", 2], ["company_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"]]
  ↳ app/controllers/daily_logs_controller.rb:217:in `current_status'
  [1m[36mDailyLog Load (0.4ms)[0m  [1m[34mSELECT "daily_logs".* FROM "daily_logs" WHERE "daily_logs"."company_id" = $1 AND "daily_logs"."id" = $2[0m  [["company_id", 2], ["id", 5]]
  ↳ app/controllers/daily_logs_controller.rb:217:in `current_status'
Completed 200 OK in 81ms (Views: 15.6ms | ActiveRecord: 5.8ms | Allocations: 19440)


user: mm
GET /daily_logs/current_status
AVOID eager loading detected
  DailyActivity => [:daily_log]
  Remove from your query: .includes([:daily_log])
Call stack
  /home/<USER>/Projects/attendifyapp/app/controllers/daily_logs_controller.rb:217:in `current_status'
  /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `block in use_locale_from_cookie_for_api'
  /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `use_locale_from_cookie_for_api'


Started GET "/daily_logs/current_status" for ************ at 2025-07-26 04:33:06 +0200
Started GET "/api/v1/daily_activities/current_work_activity" for ************ at 2025-07-26 04:33:06 +0200
[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /daily_logs/current_status
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/daily_logs/current_status", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
Processing by DailyLogsController#current_status as JSON
  [1m[36mUser Load (0.7ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mCompany Load (0.4ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (0.4ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (0.3ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
Rails controller JWT authentication successful for user: 2
Rails controller authenticated via JWT for user: 2
Using JWT tenant context: company_id=2
  [1m[36mContract Load (0.4ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:335:in `set_contract'
  [1m[36mDailyLog Load (0.7ms)[0m  [1m[34mSELECT "daily_logs".* FROM "daily_logs" WHERE "daily_logs"."company_id" = $1 AND "daily_logs"."user_id" = $2 AND "daily_logs"."contract_id" = $3 AND "daily_logs"."start_time" BETWEEN $4 AND $5 ORDER BY "daily_logs"."created_at" DESC LIMIT $6[0m  [["company_id", 2], ["user_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:192:in `current_status'
[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /api/v1/daily_activities/current_work_activity
  [1m[36mEvent Load (1.9ms)[0m  [1m[34mSELECT "events".* FROM "events" WHERE "events"."company_id" = $1 AND "events"."user_id" = $2 AND "events"."contract_id" = $3 AND "events"."status" = $4 AND (event_type < 5) AND "events"."start_time" BETWEEN $5 AND $6 ORDER BY "events"."id" ASC LIMIT $7[0m  [["company_id", 2], ["user_id", 2], ["contract_id", 1], ["status", "approved"], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:198:in `current_status'
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/api/v1/daily_activities/current_work_activity", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
  [1m[36mBreak Load (2.9ms)[0m  [1m[34mSELECT "breaks".* FROM "breaks" WHERE "breaks"."user_id" = $1 AND "breaks"."contract_id" = $2 AND "breaks"."start_time" BETWEEN $3 AND $4 ORDER BY "breaks"."id" ASC LIMIT $5[0m  [["user_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:204:in `current_status'
  [1m[36mUserSetting Load (3.4ms)[0m  [1m[34mSELECT "user_settings".* FROM "user_settings" WHERE "user_settings"."user_id" = $1 LIMIT $2[0m  [["user_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:215:in `current_status'
Processing by Api::V1::DailyActivitiesController#current_work_activity as JSON
No JWT tenant context available
  [1m[36mDailyActivity Load (7.3ms)[0m  [1m[34mSELECT "daily_activities".* FROM "daily_activities" WHERE "daily_activities"."user_id" = $1 AND "daily_activities"."company_id" = $2 AND "daily_activities"."contract_id" = $3 AND "daily_activities"."start_time" BETWEEN $4 AND $5 ORDER BY "daily_activities"."created_at" DESC[0m  [["user_id", 2], ["company_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"]]
  ↳ app/controllers/daily_logs_controller.rb:217:in `current_status'
  [1m[36mDailyLog Load (0.8ms)[0m  [1m[34mSELECT "daily_logs".* FROM "daily_logs" WHERE "daily_logs"."company_id" = $1 AND "daily_logs"."id" = $2[0m  [["company_id", 2], ["id", 5]]
  ↳ app/controllers/daily_logs_controller.rb:217:in `current_status'
  [1m[36mUser Load (2.9ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
Completed 200 OK in 113ms (Views: 24.6ms | ActiveRecord: 19.1ms | Allocations: 26390)


user: mm
GET /daily_logs/current_status
AVOID eager loading detected
  DailyActivity => [:daily_log]
  Remove from your query: .includes([:daily_log])
Call stack
  /home/<USER>/Projects/attendifyapp/app/controllers/daily_logs_controller.rb:217:in `current_status'
  /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `block in use_locale_from_cookie_for_api'
  /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `use_locale_from_cookie_for_api'


  [1m[36mCompany Load (1.2ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (0.4ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (0.9ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
API authenticated via JWT for user: 2
  [1m[36mContract Load (0.5ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/daily_activities_controller.rb:159:in `set_contract'
  [1m[36mDailyActivity Load (0.6ms)[0m  [1m[34mSELECT "daily_activities".* FROM "daily_activities" WHERE "daily_activities"."user_id" = $1 AND "daily_activities"."company_id" = $2 AND "daily_activities"."end_time" IS NULL AND "daily_activities"."activity_type" IN ($3, $4, $5) ORDER BY "daily_activities"."id" ASC LIMIT $6[0m  [["user_id", 2], ["company_id", 2], ["activity_type", "travel_to_work"], ["activity_type", "work_at_location"], ["activity_type", "work_remote"], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/daily_activities_controller.rb:18:in `current_work_activity'
  [1m[36mWork Load (2.0ms)[0m  [1m[34mSELECT "works".* FROM "works" WHERE "works"."company_id" = $1 AND "works"."id" = $2[0m  [["company_id", 2], ["id", 3]]
  ↳ app/controllers/api/v1/daily_activities_controller.rb:18:in `current_work_activity'
  [1m[36mWorkAssignment Load (0.6ms)[0m  [1m[34mSELECT "work_assignments".* FROM "work_assignments" WHERE "work_assignments"."company_id" = $1 AND "work_assignments"."id" = $2[0m  [["company_id", 2], ["id", 3]]
  ↳ app/controllers/api/v1/daily_activities_controller.rb:18:in `current_work_activity'
Started GET "/daily_logs/current_status" for ************ at 2025-07-26 04:33:06 +0200
  [1m[36mWorkSession Load (0.8ms)[0m  [1m[34mSELECT "work_sessions".* FROM "work_sessions" WHERE "work_sessions"."company_id" = $1 AND "work_sessions"."daily_activity_id" = $2 AND "work_sessions"."status" = $3 LIMIT $4[0m  [["company_id", 2], ["daily_activity_id", 10], ["status", "0"], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/daily_activities_controller.rb:21:in `current_work_activity'
[AuthMetrics] JWT request tracked for user: 2
Completed 200 OK in 161ms (Views: 1.5ms | ActiveRecord: 19.7ms | Allocations: 39612)


Started GET "/api/v1/works/today" for ************ at 2025-07-26 04:33:06 +0200
[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /daily_logs/current_status
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/daily_logs/current_status", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
Processing by DailyLogsController#current_status as JSON
  [1m[36mUser Load (0.6ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mCompany Load (0.6ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  [1m[36mCompanyUserRole Load (0.5ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (0.5ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
Rails controller JWT authentication successful for user: 2
Rails controller authenticated via JWT for user: 2
Using JWT tenant context: company_id=2
  [1m[36mContract Load (0.6ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:335:in `set_contract'
  [1m[36mDailyLog Load (0.4ms)[0m  [1m[34mSELECT "daily_logs".* FROM "daily_logs" WHERE "daily_logs"."company_id" = $1 AND "daily_logs"."user_id" = $2 AND "daily_logs"."contract_id" = $3 AND "daily_logs"."start_time" BETWEEN $4 AND $5 ORDER BY "daily_logs"."created_at" DESC LIMIT $6[0m  [["company_id", 2], ["user_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:192:in `current_status'
[SECURITY] Rack::Attack blocked request: safelist - ************ - GET /api/v1/works/today
  [1m[36mEvent Load (1.3ms)[0m  [1m[34mSELECT "events".* FROM "events" WHERE "events"."company_id" = $1 AND "events"."user_id" = $2 AND "events"."contract_id" = $3 AND "events"."status" = $4 AND (event_type < 5) AND "events"."start_time" BETWEEN $5 AND $6 ORDER BY "events"."id" ASC LIMIT $7[0m  [["company_id", 2], ["user_id", 2], ["contract_id", 1], ["status", "approved"], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:198:in `current_status'
[SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/api/v1/works/today", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
  [1m[36mBreak Load (7.9ms)[0m  [1m[34mSELECT "breaks".* FROM "breaks" WHERE "breaks"."user_id" = $1 AND "breaks"."contract_id" = $2 AND "breaks"."start_time" BETWEEN $3 AND $4 ORDER BY "breaks"."id" ASC LIMIT $5[0m  [["user_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:204:in `current_status'
Processing by Api::V1::WorksController#today as JSON
No JWT tenant context available
  [1m[36mUserSetting Load (1.9ms)[0m  [1m[34mSELECT "user_settings".* FROM "user_settings" WHERE "user_settings"."user_id" = $1 LIMIT $2[0m  [["user_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/daily_logs_controller.rb:215:in `current_status'
  [1m[36mUser Load (1.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
  [1m[36mDailyActivity Load (1.8ms)[0m  [1m[34mSELECT "daily_activities".* FROM "daily_activities" WHERE "daily_activities"."user_id" = $1 AND "daily_activities"."company_id" = $2 AND "daily_activities"."contract_id" = $3 AND "daily_activities"."start_time" BETWEEN $4 AND $5 ORDER BY "daily_activities"."created_at" DESC[0m  [["user_id", 2], ["company_id", 2], ["contract_id", 1], ["start_time", "2025-07-25 22:00:00"], ["start_time", "2025-07-26 21:59:59.999999"]]
  ↳ app/controllers/daily_logs_controller.rb:217:in `current_status'
  [1m[36mCompany Load (7.6ms)[0m  [1m[34mSELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2[0m  [["id", 2], ["LIMIT", 1]]
  [1m[36mDailyLog Load (0.7ms)[0m  [1m[34mSELECT "daily_logs".* FROM "daily_logs" WHERE "daily_logs"."company_id" = $1 AND "daily_logs"."id" = $2[0m  [["company_id", 2], ["id", 5]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
  ↳ app/controllers/daily_logs_controller.rb:217:in `current_status'
Completed 200 OK in 121ms (Views: 29.8ms | ActiveRecord: 16.9ms | Allocations: 27988)


user: mm
GET /daily_logs/current_status
AVOID eager loading detected
  DailyActivity => [:daily_log]
  Remove from your query: .includes([:daily_log])
Call stack
  /home/<USER>/Projects/attendifyapp/app/controllers/daily_logs_controller.rb:217:in `current_status'
  /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `block in use_locale_from_cookie_for_api'
  /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `use_locale_from_cookie_for_api'


  [1m[36mCompanyUserRole Load (10.1ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5[0m  [["active", true], ["user_id", 2], ["active", true], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
  [1m[36mRole Load (0.5ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
JWT tenant context set: company_id=2 for user=2 with role=owner
[SECURITY] tenant_context_set: {:severity=>"low", :user_id=>2, :company_id=>2, :role=>"owner"}
API authenticated via JWT for user: 2
  [1m[36mContract Load (0.5ms)[0m  [1m[34mSELECT "contracts".* FROM "contracts" WHERE "contracts"."company_id" = $1 AND "contracts"."user_id" = $2 AND "contracts"."company_id" = $3 LIMIT $4[0m  [["company_id", 2], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/controllers/api/v1/works_controller.rb:232:in `set_contract'
  [1m[36mCompanyUserRole Load (0.6ms)[0m  [1m[34mSELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."company_id" = $3 LIMIT $4[0m  [["active", true], ["user_id", 2], ["company_id", 2], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
  [1m[36mCACHE Role Load (0.0ms)[0m  [1m[34mSELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2[0m  [["id", 1], ["LIMIT", 1]]
  ↳ app/models/user.rb:76:in `role_in'
  [1m[36mWork Load (1.1ms)[0m  [1m[34mSELECT "works".* FROM "works" WHERE "works"."company_id" = $1 AND "works"."id" IN (SELECT "work_assignments"."work_id" FROM "work_assignments" WHERE "work_assignments"."company_id" = $2 AND "work_assignments"."contract_id" = $3) AND (scheduled_start_date >= '2025-07-26') AND "works"."status" IN ($4, $5, $6) ORDER BY "works"."scheduled_start_date" ASC, "works"."created_at" ASC[0m  [["company_id", 2], ["company_id", 2], ["contract_id", 1], ["status", "scheduled"], ["status", "in_progress"], ["status", "unprocessed"]]
  ↳ app/controllers/api/v1/works_controller.rb:86:in `today'
[AuthMetrics] JWT request tracked for user: 2
Completed 200 OK in 115ms (Views: 0.3ms | ActiveRecord: 21.9ms | Allocations: 29926)


Redis Namespacing Configuration:
  jwt_sessions: tymboxapp:jwt:sessions
  jwt_revoked: tymboxapp:jwt:revoked
  jwt_refresh: tymboxapp:jwt:refresh
  password_reset: tymboxapp:auth:password_reset
  reset_token: tymboxapp:auth:reset_token
  email_confirmation: tymboxapp:auth:email_confirmation
  confirmation_token: tymboxapp:auth:confirmation_token
  invitation_token: tymboxapp:auth:invitation_token
  invitation_by_email: tymboxapp:auth:invitation_by_email
  rate_limit: tymboxapp:rate_limit
  cache: tymboxapp:cache
  sidekiq: tymboxapp:sidekiq
  websocket: tymboxapp:ws
  metrics: tymboxapp:metrics
  notifications: tymboxapp:notifications
  sessions: tymboxapp:sessions
  temp_data: tymboxapp:temp
  analytics: tymboxapp:analytics
  audit_log: tymboxapp:audit
  performance: tymboxapp:perf
  booking: tymboxapp:booking
  company: tymboxapp:company
  user_activity: tymboxapp:activity
  reports: tymboxapp:reports
[JWT-Only] Session store disabled - using JWT authentication only
[FeatureFlags] Current status: {"jwt":{"enabled":true,"login_enabled":true,"api_enabled":true,"dual_auth":false,"only_mode":true},"session":{"disabled":true},"pwa":{"enabled":false,"service_worker":false,"offline_mode":false},"debug":{"auth_debug":false,"auth_monitoring":true},"emergency":{"rollback_enabled":false}}
Started GET "/api/v1/dashboard" for ::1 at 2025-07-26 07:32:08 +0200
  [1m[36mActiveRecord::SchemaMigration Pluck (1.8ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
[SECURITY] Rack::Attack blocked request: safelist - ::1 - GET /api/v1/dashboard
[SECURITY] rate_limit_violation: {:ip=>"::1", :path=>"/api/v1/dashboard", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-localhost"}
  
ActionController::RoutingError (No route matches [GET] "/api/v1/dashboard"):
  
Redis Namespacing Configuration:
  jwt_sessions: tymboxapp:jwt:sessions
  jwt_revoked: tymboxapp:jwt:revoked
  jwt_refresh: tymboxapp:jwt:refresh
  password_reset: tymboxapp:auth:password_reset
  reset_token: tymboxapp:auth:reset_token
  email_confirmation: tymboxapp:auth:email_confirmation
  confirmation_token: tymboxapp:auth:confirmation_token
  invitation_token: tymboxapp:auth:invitation_token
  invitation_by_email: tymboxapp:auth:invitation_by_email
  rate_limit: tymboxapp:rate_limit
  cache: tymboxapp:cache
  sidekiq: tymboxapp:sidekiq
  websocket: tymboxapp:ws
  metrics: tymboxapp:metrics
  notifications: tymboxapp:notifications
  sessions: tymboxapp:sessions
  temp_data: tymboxapp:temp
  analytics: tymboxapp:analytics
  audit_log: tymboxapp:audit
  performance: tymboxapp:perf
  booking: tymboxapp:booking
  company: tymboxapp:company
  user_activity: tymboxapp:activity
  reports: tymboxapp:reports
[JWT-Only] Session store disabled - using JWT authentication only
[FeatureFlags] Current status: {"jwt":{"enabled":true,"login_enabled":true,"api_enabled":true,"dual_auth":false,"only_mode":true},"session":{"disabled":true},"pwa":{"enabled":false,"service_worker":false,"offline_mode":false},"debug":{"auth_debug":false,"auth_monitoring":true},"emergency":{"rollback_enabled":false}}
